[tool.poetry]
name = "jommcp"
version = "1.0.0"
description = "Transform your APIs into AI-ready MCP servers"
authors = ["JomMCP Team <<EMAIL>>"]
readme = "README.md"
homepage = "https://github.com/amanasmuei/JomMCP"
repository = "https://github.com/amanasmuei/JomMCP"
documentation = "https://docs.jommcp.com"
keywords = ["api", "mcp", "ai", "automation", "server"]
packages = [
    {include = "packages/core"},
    {include = "apps/api-gateway"},
    {include = "apps/registration-service"},
    {include = "apps/generator-service"},
    {include = "apps/deployment-service"},
    {include = "apps/docs-service"}
]
classifiers = [
    "Development Status :: 5 - Production/Stable",
    "Intended Audience :: Developers",
    "License :: OSI Approved :: MIT License",
    "Operating System :: OS Independent",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.11",
    "Programming Language :: Python :: 3.12",
    "Topic :: Software Development :: Libraries :: Python Modules",
    "Topic :: Internet :: WWW/HTTP :: HTTP Servers",
    "Topic :: System :: Networking",
]

[tool.poetry.dependencies]
python = "^3.11"
fastapi = "^0.104.1"
uvicorn = {extras = ["standard"], version = "^0.24.0"}
sqlalchemy = "^2.0.23"
alembic = "^1.13.0"
asyncpg = "^0.29.0"
psycopg2-binary = "^2.9.9"
redis = "^5.0.1"
python-jose = {extras = ["cryptography"], version = "^3.3.0"}
passlib = {extras = ["bcrypt"], version = "^1.7.4"}
python-multipart = "^0.0.6"
pydantic = {extras = ["email"], version = "^2.5.0"}
pydantic-settings = "^2.1.0"
jinja2 = "^3.1.2"
aiofiles = "^23.2.1"
httpx = "^0.25.2"
cryptography = "^41.0.7"
kubernetes = "^28.1.0"
docker = "^6.1.3"
prometheus-client = "^0.19.0"
structlog = "^23.2.0"
mcp = "^1.0.0"
openapi-spec-validator = "^0.7.1"
graphql-core = "^3.2.3"
celery = "^5.3.4"
flower = "^2.0.1"

[tool.poetry.group.dev.dependencies]
pytest = "^7.4.3"
pytest-asyncio = "^0.21.1"
pytest-cov = "^4.1.0"
black = "^23.11.0"
isort = "^5.12.0"
flake8 = "^6.1.0"
mypy = "^1.7.1"
pre-commit = "^3.6.0"
httpx = "^0.25.2"
pytest-mock = "^3.12.0"
factory-boy = "^3.3.0"
faker = "^20.1.0"

[tool.poetry.group.test.dependencies]
testcontainers = "^3.7.1"
pytest-postgresql = "^5.0.0"
pytest-redis = "^3.0.2"

[build-system]
requires = ["poetry-core"]
build-backend = "poetry.core.masonry.api"

[tool.black]
line-length = 88
target-version = ['py311']
include = '\.pyi?$'
extend-exclude = '''
/(
  # directories
  \.eggs
  | \.git
  | \.hg
  | \.mypy_cache
  | \.tox
  | \.venv
  | build
  | dist
)/
'''

[tool.isort]
profile = "black"
multi_line_output = 3
line_length = 88
known_first_party = ["packages.core", "apps"]

[tool.mypy]
python_version = "3.11"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true
disallow_incomplete_defs = true
check_untyped_defs = true
disallow_untyped_decorators = true
no_implicit_optional = true
warn_redundant_casts = true
warn_unused_ignores = true
warn_no_return = true
warn_unreachable = true
strict_equality = true

[[tool.mypy.overrides]]
module = [
    "kubernetes.*",
    "docker.*",
    "celery.*",
    "flower.*",
    "mcp.*"
]
ignore_missing_imports = true

[tool.pytest.ini_options]
minversion = "7.0"
addopts = "-ra -q --strict-markers --strict-config"
testpaths = ["tests"]
markers = [
    "slow: marks tests as slow (deselect with '-m \"not slow\"')",
    "integration: marks tests as integration tests",
    "unit: marks tests as unit tests",
    "e2e: marks tests as end-to-end tests",
]

[tool.coverage.run]
source = ["packages", "apps"]
omit = [
    "*/tests/*",
    "*/migrations/*",
    "*/__init__.py",
    "*/conftest.py",
]

[tool.coverage.report]
exclude_lines = [
    "pragma: no cover",
    "def __repr__",
    "if self.debug:",
    "if settings.DEBUG",
    "raise AssertionError",
    "raise NotImplementedError",
    "if 0:",
    "if __name__ == .__main__.:",
    "class .*\bProtocol\):",
    "@(abc\.)?abstractmethod",
]
