# MCP Hub Platform Environment Configuration

# Application Settings
APP_NAME=MCP Hub Platform
APP_VERSION=1.0.0
ENVIRONMENT=development
DEBUG=true

# Database Configuration
DATABASE_URL=postgresql+asyncpg://mcphub:mcphub_dev_password@localhost:5432/mcphub
DATABASE_ECHO=false
DATABASE_POOL_SIZE=20

# Redis Configuration
REDIS_URL=redis://localhost:6379/0
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_DB=0

# Security Configuration
SECRET_KEY=your-secret-key-here-change-in-production
ENCRYPTION_KEY=your-encryption-key-here-change-in-production
JWT_ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=30
REFRESH_TOKEN_EXPIRE_DAYS=7

# API Configuration
API_V1_PREFIX=/api/v1
CORS_ORIGINS=["http://localhost:3000","http://localhost:8080"]

# Rate Limiting
RATE_LIMIT_ENABLED=true
RATE_LIMIT_REQUESTS_PER_MINUTE=100
RATE_LIMIT_BURST_CAPACITY=200

# Docker Configuration
DOCKER_REGISTRY_URL=localhost:5000
DOCKER_REGISTRY_NAMESPACE=mcphub
DOCKER_BUILD_TIMEOUT_MINUTES=10
DOCKER_MEMORY_LIMIT=2g
DOCKER_CPU_LIMIT=1

# Kubernetes Configuration
KUBERNETES_NAMESPACE=mcphub
KUBERNETES_IN_CLUSTER=false

# Code Generation
TEMPLATE_PATH=templates
CODE_OUTPUT_DIR=/tmp/mcphub/generated
MAX_CONCURRENT_GENERATIONS=5

# Monitoring
PROMETHEUS_ENABLED=true
PROMETHEUS_PORT=8000
LOG_LEVEL=INFO
STRUCTURED_LOGGING=true

# Frontend Configuration (for web-ui)
NEXT_PUBLIC_API_BASE_URL=http://localhost:8081
NEXT_PUBLIC_APP_NAME=MCP Hub Platform
NEXT_PUBLIC_APP_VERSION=1.0.0
