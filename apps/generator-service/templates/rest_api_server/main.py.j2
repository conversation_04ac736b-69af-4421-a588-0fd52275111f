"""
Generated MCP Server for {{ api_name }}
{{ api_description }}
"""

import asyncio
import json
from typing import Any, Dict, List, Optional

from mcp.server import Server
from mcp.server.models import InitializationOptions
from mcp.server.stdio import stdio_server
from mcp.types import (
    CallToolRequest,
    CallToolResult,
    ListToolsRequest,
    ListToolsResult,
    Tool,
    TextContent,
)
import httpx
import structlog

logger = structlog.get_logger()

# Server configuration
SERVER_NAME = "{{ server_name }}"
SERVER_VERSION = "1.0.0"
API_BASE_URL = "{{ base_url }}"

# Authentication configuration
AUTH_TYPE = "{{ authentication_type }}"
{% if credentials %}
CREDENTIALS = {{ credentials | tojson }}
{% else %}
CREDENTIALS = {}
{% endif %}

class {{ server_name.replace(' ', '').replace('-', '') }}MCPServer:
    """MCP Server for {{ api_name }}."""
    
    def __init__(self):
        self.server = Server(SERVER_NAME)
        self.client = httpx.AsyncClient()
        self._setup_tools()
    
    def _setup_tools(self):
        """Setup MCP tools based on API specification."""
        
        @self.server.list_tools()
        async def list_tools() -> List[Tool]:
            """List available tools."""
            return [
                Tool(
                    name="api_call",
                    description="Make a call to the {{ api_name }} API",
                    inputSchema={
                        "type": "object",
                        "properties": {
                            "endpoint": {
                                "type": "string",
                                "description": "API endpoint path"
                            },
                            "method": {
                                "type": "string",
                                "enum": ["GET", "POST", "PUT", "DELETE"],
                                "default": "GET",
                                "description": "HTTP method"
                            },
                            "params": {
                                "type": "object",
                                "description": "Query parameters"
                            },
                            "data": {
                                "type": "object",
                                "description": "Request body data"
                            }
                        },
                        "required": ["endpoint"]
                    }
                )
            ]
        
        @self.server.call_tool()
        async def call_tool(name: str, arguments: Dict[str, Any]) -> CallToolResult:
            """Handle tool calls."""
            if name == "api_call":
                return await self._handle_api_call(arguments)
            else:
                raise ValueError(f"Unknown tool: {name}")
    
    async def _handle_api_call(self, arguments: Dict[str, Any]) -> CallToolResult:
        """Handle API call tool."""
        try:
            endpoint = arguments.get("endpoint", "")
            method = arguments.get("method", "GET").upper()
            params = arguments.get("params", {})
            data = arguments.get("data", {})
            
            # Construct full URL
            url = f"{API_BASE_URL.rstrip('/')}/{endpoint.lstrip('/')}"
            
            # Prepare headers
            headers = {"User-Agent": f"{SERVER_NAME}/{SERVER_VERSION}"}
            
            # Add authentication
            if AUTH_TYPE == "bearer_token" and "token" in CREDENTIALS:
                headers["Authorization"] = f"Bearer {CREDENTIALS['token']}"
            elif AUTH_TYPE == "api_key" and "api_key" in CREDENTIALS:
                header_name = CREDENTIALS.get("header_name", "X-API-Key")
                headers[header_name] = CREDENTIALS["api_key"]
            
            # Make request
            response = await self.client.request(
                method=method,
                url=url,
                params=params,
                json=data if data else None,
                headers=headers
            )
            
            # Parse response
            try:
                response_data = response.json()
            except:
                response_data = response.text
            
            result = {
                "status_code": response.status_code,
                "headers": dict(response.headers),
                "data": response_data
            }
            
            return CallToolResult(
                content=[
                    TextContent(
                        type="text",
                        text=json.dumps(result, indent=2)
                    )
                ]
            )
            
        except Exception as e:
            logger.error("API call failed", error=str(e))
            return CallToolResult(
                content=[
                    TextContent(
                        type="text",
                        text=f"Error: {str(e)}"
                    )
                ],
                isError=True
            )
    
    async def run(self):
        """Run the MCP server."""
        async with stdio_server() as (read_stream, write_stream):
            await self.server.run(
                read_stream,
                write_stream,
                InitializationOptions(
                    server_name=SERVER_NAME,
                    server_version=SERVER_VERSION,
                    capabilities=self.server.get_capabilities(
                        notification_options=None,
                        experimental_capabilities={}
                    )
                )
            )

async def main():
    """Main entry point."""
    server = {{ server_name.replace(' ', '').replace('-', '') }}MCPServer()
    await server.run()

if __name__ == "__main__":
    asyncio.run(main())
