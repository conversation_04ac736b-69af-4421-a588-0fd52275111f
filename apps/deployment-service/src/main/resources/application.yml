spring:
  application:
    name: deployment-service
  
  profiles:
    active: ${SPRING_PROFILES_ACTIVE:dev}
  
  datasource:
    url: jdbc:postgresql://${DB_HOST:localhost}:${DB_PORT:5432}/${DB_NAME:mcphub}
    username: ${DB_USERNAME:mcphub}
    password: ${DB_PASSWORD:mcphub_dev_password}
    driver-class-name: org.postgresql.Driver
    hikari:
      maximum-pool-size: 20
      minimum-idle: 5
      idle-timeout: 300000
      connection-timeout: 20000
  
  jpa:
    hibernate:
      ddl-auto: validate
    show-sql: false
    properties:
      hibernate:
        dialect: org.hibernate.dialect.PostgreSQLDialect
  
  security:
    oauth2:
      resourceserver:
        jwt:
          issuer-uri: ${JWT_ISSUER_URI:http://localhost:8080/auth/realms/mcphub}

server:
  port: ${SERVER_PORT:8083}
  servlet:
    context-path: /api/v1

management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics,prometheus
  endpoint:
    health:
      show-details: when-authorized

logging:
  level:
    com.mcphub: ${LOG_LEVEL:INFO}

# Application specific configuration
mcphub:
  deployment:
    docker:
      host: ${DOCKER_HOST:unix:///var/run/docker.sock}
      tls-verify: false
      registry:
        url: ${DOCKER_REGISTRY_URL:localhost:5000}
        username: ${DOCKER_REGISTRY_USERNAME:}
        password: ${DOCKER_REGISTRY_PASSWORD:}
    
    kubernetes:
      enabled: ${KUBERNETES_ENABLED:false}
      namespace: ${KUBERNETES_NAMESPACE:mcphub}
      config-path: ${KUBERNETES_CONFIG_PATH:~/.kube/config}
    
    health-check:
      enabled: true
      interval-seconds: 30
      timeout-seconds: 10
      failure-threshold: 3
      success-threshold: 1
    
    scaling:
      enabled: true
      min-replicas: 1
      max-replicas: 10
      target-cpu-utilization: 70
      scale-up-cooldown-seconds: 300
      scale-down-cooldown-seconds: 600
    
    resource-limits:
      default-cpu-limit: 500m
      default-memory-limit: 512Mi
      max-cpu-limit: 2
      max-memory-limit: 4Gi
    
    networking:
      port-range-start: 8000
      port-range-end: 9000
      load-balancer-enabled: false
    
    async:
      core-pool-size: 5
      max-pool-size: 20
      queue-capacity: 100

---
# Development profile
spring:
  config:
    activate:
      on-profile: dev

logging:
  level:
    com.mcphub: DEBUG

mcphub:
  deployment:
    health-check:
      interval-seconds: 60 # Less frequent in dev
    scaling:
      enabled: false # Disable auto-scaling in dev
    resource-limits:
      default-cpu-limit: 200m
      default-memory-limit: 256Mi

---
# Production profile
spring:
  config:
    activate:
      on-profile: prod

mcphub:
  deployment:
    kubernetes:
      enabled: true
    health-check:
      interval-seconds: 15 # More frequent in prod
    scaling:
      max-replicas: 50
      target-cpu-utilization: 60
    networking:
      load-balancer-enabled: true
