spring:
  application:
    name: registration-service
  
  profiles:
    active: ${SPRING_PROFILES_ACTIVE:dev}
  
  datasource:
    url: jdbc:postgresql://${DB_HOST:localhost}:${DB_PORT:5432}/${DB_NAME:mcphub}
    username: ${DB_USERNAME:mcphub}
    password: ${DB_PASSWORD:mcphub_dev_password}
    driver-class-name: org.postgresql.Driver
    hikari:
      maximum-pool-size: 20
      minimum-idle: 5
      idle-timeout: 300000
      connection-timeout: 20000
      validation-timeout: 5000
  
  jpa:
    hibernate:
      ddl-auto: validate
    show-sql: false
    properties:
      hibernate:
        dialect: org.hibernate.dialect.PostgreSQLDialect
        format_sql: true
        use_sql_comments: true
        jdbc:
          batch_size: 20
        order_inserts: true
        order_updates: true
  
  flyway:
    enabled: true
    baseline-on-migrate: true
    validate-on-migrate: true
    locations: classpath:db/migration
  
  redis:
    host: ${REDIS_HOST:localhost}
    port: ${REDIS_PORT:6379}
    password: ${REDIS_PASSWORD:}
    timeout: 2000ms
    lettuce:
      pool:
        max-active: 8
        max-idle: 8
        min-idle: 0
  
  security:
    oauth2:
      resourceserver:
        jwt:
          issuer-uri: ${JWT_ISSUER_URI:http://localhost:8080/auth/realms/mcphub}
  
  cache:
    type: redis
    redis:
      time-to-live: 600000 # 10 minutes
  
  web:
    cors:
      allowed-origins: ${CORS_ALLOWED_ORIGINS:http://localhost:3000,http://localhost:8080}
      allowed-methods: GET,POST,PUT,DELETE,OPTIONS
      allowed-headers: "*"
      allow-credentials: true

server:
  port: ${SERVER_PORT:8081}
  servlet:
    context-path: /api/v1
  compression:
    enabled: true
    mime-types: application/json,application/xml,text/html,text/xml,text/plain

management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics,prometheus
  endpoint:
    health:
      show-details: when-authorized
  metrics:
    export:
      prometheus:
        enabled: true

logging:
  level:
    com.mcphub: ${LOG_LEVEL:INFO}
    org.springframework.security: ${SECURITY_LOG_LEVEL:WARN}
    org.hibernate.SQL: ${SQL_LOG_LEVEL:WARN}
    org.hibernate.type.descriptor.sql.BasicBinder: ${SQL_BIND_LOG_LEVEL:WARN}
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} - %msg%n"
    file: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"

# Application specific configuration
mcphub:
  registration:
    validation:
      enabled: true
      timeout-seconds: 30
      retry-attempts: 3
      retry-delay-seconds: 5
    
    encryption:
      algorithm: AES
      key-length: 256
      secret-key: ${ENCRYPTION_SECRET_KEY:mySecretKey123456789012345678901234567890}
    
    rate-limiting:
      enabled: true
      requests-per-minute: 100
      burst-capacity: 200
    
    api-types:
      openapi:
        max-spec-size-mb: 10
        timeout-seconds: 30
      graphql:
        max-schema-size-mb: 5
        introspection-timeout-seconds: 30
    
    async:
      core-pool-size: 5
      max-pool-size: 20
      queue-capacity: 100

# Jasypt encryption configuration
jasypt:
  encryptor:
    password: ${JASYPT_ENCRYPTOR_PASSWORD:myEncryptorPassword}
    algorithm: PBEWITHHMACSHA512ANDAES_256
    iv-generator-classname: org.jasypt.iv.RandomIvGenerator

---
# Development profile
spring:
  config:
    activate:
      on-profile: dev
  
  jpa:
    show-sql: true
    properties:
      hibernate:
        format_sql: true

logging:
  level:
    com.mcphub: DEBUG
    org.springframework.web: DEBUG

mcphub:
  registration:
    validation:
      enabled: false # Disable validation in dev for faster testing

---
# Production profile
spring:
  config:
    activate:
      on-profile: prod
  
  jpa:
    show-sql: false
  
  redis:
    ssl: true

logging:
  level:
    com.mcphub: INFO
    org.springframework.security: WARN

mcphub:
  registration:
    rate-limiting:
      requests-per-minute: 60
      burst-capacity: 120
