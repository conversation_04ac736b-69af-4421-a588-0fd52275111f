{"name": "jommcp-web-ui", "version": "1.0.0", "description": "JomMCP Platform - Web UI Frontend", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "type-check": "tsc --noEmit", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage"}, "dependencies": {"@hookform/resolvers": "^3.3.4", "@radix-ui/react-avatar": "^1.0.4", "@radix-ui/react-dialog": "^1.0.5", "@radix-ui/react-dropdown-menu": "^2.0.6", "@radix-ui/react-label": "^2.0.2", "@radix-ui/react-progress": "^1.0.3", "@radix-ui/react-select": "^2.0.0", "@radix-ui/react-separator": "^1.0.3", "@radix-ui/react-slot": "^1.0.2", "@radix-ui/react-switch": "^1.0.3", "@radix-ui/react-tabs": "^1.0.4", "@radix-ui/react-toast": "^1.1.5", "@radix-ui/react-tooltip": "^1.0.7", "@tabler/icons-react": "^3.33.0", "@tailwindcss/forms": "^0.5.7", "@tailwindcss/typography": "^0.5.10", "@tanstack/react-query": "^5.32.0", "@tanstack/react-query-devtools": "^5.32.0", "@types/node": "^20.12.0", "@types/react": "^18.3.0", "@types/react-dom": "^18.3.0", "aceternity-ui": "^0.2.2", "autoprefixer": "^10.4.0", "axios": "^1.6.8", "class-variance-authority": "^0.7.0", "clsx": "^2.1.0", "date-fns": "^3.6.0", "framer-motion": "^11.1.0", "jose": "^5.2.4", "js-cookie": "^3.0.5", "lucide-react": "^0.376.0", "mini-svg-data-uri": "^1.4.4", "next": "^14.2.0", "next-themes": "^0.3.0", "postcss": "^8.4.0", "prismjs": "^1.30.0", "react": "^18.3.0", "react-dom": "^18.3.0", "react-hook-form": "^7.51.0", "react-hot-toast": "^2.4.1", "react-syntax-highlighter": "^15.6.1", "recharts": "^2.12.0", "simplex-noise": "^4.0.1", "tailwind-merge": "^2.3.0", "tailwindcss": "^3.4.0", "three": "^0.160.0", "typescript": "^5.4.0", "use-debounce": "^10.0.0", "zod": "^3.23.0"}, "devDependencies": {"@testing-library/jest-dom": "^6.4.0", "@testing-library/react": "^15.0.0", "@types/js-cookie": "^3.0.6", "@types/react-syntax-highlighter": "^15.5.11", "@types/three": "^0.160.0", "@typescript-eslint/eslint-plugin": "^7.7.0", "@typescript-eslint/parser": "^7.7.0", "eslint": "^8.57.0", "eslint-config-next": "^14.2.0", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "prettier": "^3.2.5", "prettier-plugin-tailwindcss": "^0.5.14"}, "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}}