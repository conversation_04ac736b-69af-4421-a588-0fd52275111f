@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;
    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;
    --primary: 221.2 83.2% 53.3%;
    --primary-foreground: 210 40% 98%;
    --secondary: 210 40% 96%;
    --secondary-foreground: 222.2 84% 4.9%;
    --muted: 210 40% 96%;
    --muted-foreground: 215.4 16.3% 46.9%;
    --accent: 210 40% 96%;
    --accent-foreground: 222.2 84% 4.9%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;
    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 221.2 83.2% 53.3%;
    --radius: 0.5rem;

    /* Custom status colors */
    --success-50: 142 76% 96%;
    --success-100: 142 69% 91%;
    --success-200: 142 69% 82%;
    --success-500: 142 69% 58%;
    --success-800: 142 69% 25%;
    --success-900: 142 69% 15%;

    --warning-50: 48 96% 89%;
    --warning-100: 48 96% 76%;
    --warning-200: 48 96% 64%;
    --warning-500: 48 96% 53%;
    --warning-800: 48 96% 25%;
    --warning-900: 48 96% 15%;

    --error-50: 0 86% 97%;
    --error-100: 0 93% 94%;
    --error-200: 0 96% 89%;
    --error-500: 0 84% 60%;
    --error-800: 0 84% 25%;
    --error-900: 0 84% 15%;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;
    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;
    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;
    --primary: 217.2 91.2% 59.8%;
    --primary-foreground: 222.2 84% 4.9%;
    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;
    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;
    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;
    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 224.3 76.3% 94.1%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
    font-feature-settings: "rlig" 1, "calt" 1;
  }
}

@layer components {
  /* JomMCP Custom Components */
  .jom-card {
    @apply bg-card text-card-foreground rounded-lg border shadow-sm;
  }

  .jom-button-primary {
    @apply bg-primary text-primary-foreground hover:bg-primary/90 focus:ring-2 focus:ring-primary focus:ring-offset-2;
  }

  .jom-button-secondary {
    @apply bg-secondary text-secondary-foreground hover:bg-secondary/80 focus:ring-2 focus:ring-secondary focus:ring-offset-2;
  }

  .jom-input {
    @apply flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50;
  }

  .jom-status-badge {
    @apply inline-flex items-center rounded-full px-2.5 py-0.5 text-xs font-medium;
  }

  .jom-status-pending {
    @apply bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200;
  }

  .jom-status-active {
    @apply bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200;
  }

  .jom-status-error {
    @apply bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200;
  }

  .jom-status-running {
    @apply bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200;
  }

  .jom-gradient-bg {
    @apply bg-gradient-to-br from-brand-50 to-brand-100 dark:from-brand-900 dark:to-brand-800;
  }

  .jom-sidebar {
    @apply bg-card border-r border-border;
  }

  .jom-nav-item {
    @apply flex items-center px-3 py-2 text-sm font-medium rounded-md transition-colors hover:bg-accent hover:text-accent-foreground;
  }

  .jom-nav-item-active {
    @apply bg-primary text-primary-foreground;
  }
}

@layer utilities {
  .text-balance {
    text-wrap: balance;
  }

  .scrollbar-hide {
    -ms-overflow-style: none;
    scrollbar-width: none;
  }

  .scrollbar-hide::-webkit-scrollbar {
    display: none;
  }

  /* Grid pattern background */
  .bg-grid-pattern {
    background-image:
      linear-gradient(rgba(0, 0, 0, 0.1) 1px, transparent 1px),
      linear-gradient(90deg, rgba(0, 0, 0, 0.1) 1px, transparent 1px);
    background-size: 20px 20px;
  }

  /* Dot pattern backgrounds */
  .bg-dot-thick-neutral-300 {
    background-image: radial-gradient(circle, #d4d4d8 1px, transparent 1px);
    background-size: 15px 15px;
  }

  .bg-dot-thick-neutral-800 {
    background-image: radial-gradient(circle, #27272a 1px, transparent 1px);
    background-size: 15px 15px;
  }

  .bg-dot-thick-indigo-500 {
    background-image: radial-gradient(circle, #6366f1 1px, transparent 1px);
    background-size: 15px 15px;
  }

  /* Custom animations */
  @keyframes float {
    0%, 100% {
      transform: translateY(0px);
    }
    50% {
      transform: translateY(-10px);
    }
  }

  .animate-float {
    animation: float 3s ease-in-out infinite;
  }

  @keyframes shimmer {
    0% {
      background-position: -200% 0;
    }
    100% {
      background-position: 200% 0;
    }
  }

  .animate-shimmer {
    animation: shimmer 2s linear infinite;
  }

  @keyframes pulse-glow {
    0%, 100% {
      box-shadow: 0 0 5px rgba(59, 130, 246, 0.5);
    }
    50% {
      box-shadow: 0 0 20px rgba(59, 130, 246, 0.8), 0 0 30px rgba(59, 130, 246, 0.6);
    }
  }

  .animate-pulse-glow {
    animation: pulse-glow 2s ease-in-out infinite;
  }

  /* Gradient text animation */
  @keyframes gradient-shift {
    0%, 100% {
      background-position: 0% 50%;
    }
    50% {
      background-position: 100% 50%;
    }
  }

  .animate-gradient {
    background-size: 200% 200%;
    animation: gradient-shift 3s ease infinite;
  }

  /* Enhanced hover effects */
  .hover-lift {
    transition: transform 0.3s ease, box-shadow 0.3s ease;
  }

  .hover-lift:hover {
    transform: translateY(-5px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  }

  /* Smooth scrolling */
  html {
    scroll-behavior: smooth;
  }
}
