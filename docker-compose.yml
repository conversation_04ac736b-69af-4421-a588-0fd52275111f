version: '3.8'

services:
  # Database
  postgres:
    image: postgres:16-alpine
    container_name: jommcp-postgres
    environment:
      POSTGRES_DB: ${POSTGRES_DB:-mcphub}
      POSTGRES_USER: ${POSTGRES_USER:-mcphub}
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD:-mcphub_dev_password}
    ports:
      - "${POSTGRES_PORT:-5432}:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./database/init:/docker-entrypoint-initdb.d
    networks:
      - jommcp-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U ${POSTGRES_USER:-mcphub}"]
      interval: 10s
      timeout: 5s
      retries: 5

  # Redis for caching and session management
  redis:
    image: redis:7-alpine
    container_name: jommcp-redis
    ports:
      - "${REDIS_PORT:-6379}:6379"
    volumes:
      - redis_data:/data
    networks:
      - jommcp-network
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 5

  # API Gateway
  api-gateway:
    build:
      context: ./apps/api-gateway
      dockerfile: Dockerfile
    container_name: jommcp-api-gateway
    environment:
      - DATABASE_URL=postgresql+asyncpg://${POSTGRES_USER:-mcphub}:${POSTGRES_PASSWORD:-mcphub_dev_password}@postgres:5432/${POSTGRES_DB:-mcphub}
      - REDIS_URL=redis://redis:6379/0
      - ENVIRONMENT=${ENVIRONMENT:-development}
      - DEBUG=${DEBUG:-true}
      - JWT_SECRET_KEY=${JWT_SECRET_KEY}
    ports:
      - "${API_GATEWAY_PORT:-8000}:8000"
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    networks:
      - jommcp-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Registration Service
  registration-service:
    build:
      context: ./apps/registration-service
      dockerfile: Dockerfile
    container_name: jommcp-registration
    environment:
      - DATABASE_URL=postgresql+asyncpg://${POSTGRES_USER:-mcphub}:${POSTGRES_PASSWORD:-mcphub_dev_password}@postgres:5432/${POSTGRES_DB:-mcphub}
      - REDIS_URL=redis://redis:6379/0
      - ENVIRONMENT=${ENVIRONMENT:-development}
      - DEBUG=${DEBUG:-true}
    ports:
      - "${REGISTRATION_SERVICE_PORT:-8081}:8081"
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    networks:
      - jommcp-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8081/api/v1/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Generator Service
  generator-service:
    build:
      context: ./apps/generator-service
      dockerfile: Dockerfile
    container_name: jommcp-generator
    environment:
      - DATABASE_URL=postgresql+asyncpg://${POSTGRES_USER:-mcphub}:${POSTGRES_PASSWORD:-mcphub_dev_password}@postgres:5432/${POSTGRES_DB:-mcphub}
      - REDIS_URL=redis://redis:6379/0
      - ENVIRONMENT=${ENVIRONMENT:-development}
      - DEBUG=${DEBUG:-true}
    ports:
      - "${GENERATOR_SERVICE_PORT:-8082}:8082"
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    networks:
      - jommcp-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8082/api/v1/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Deployment Service
  deployment-service:
    build:
      context: ./apps/deployment-service
      dockerfile: Dockerfile
    container_name: jommcp-deployment
    environment:
      - DATABASE_URL=postgresql+asyncpg://${POSTGRES_USER:-mcphub}:${POSTGRES_PASSWORD:-mcphub_dev_password}@postgres:5432/${POSTGRES_DB:-mcphub}
      - REDIS_URL=redis://redis:6379/0
      - ENVIRONMENT=${ENVIRONMENT:-development}
      - DEBUG=${DEBUG:-true}
    ports:
      - "${DEPLOYMENT_SERVICE_PORT:-8083}:8083"
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    volumes:
      - /var/run/docker.sock:/var/run/docker.sock
    networks:
      - jommcp-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8083/api/v1/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Documentation Service
  docs-service:
    build:
      context: ./apps/docs-service
      dockerfile: Dockerfile
    container_name: jommcp-docs
    environment:
      - DATABASE_URL=postgresql+asyncpg://${POSTGRES_USER:-mcphub}:${POSTGRES_PASSWORD:-mcphub_dev_password}@postgres:5432/${POSTGRES_DB:-mcphub}
      - REDIS_URL=redis://redis:6379/0
      - ENVIRONMENT=${ENVIRONMENT:-development}
      - DEBUG=${DEBUG:-true}
      - DOCS_OUTPUT_DIRECTORY=/app/docs_output
    ports:
      - "${DOCS_SERVICE_PORT:-8084}:8084"
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    volumes:
      - docs_data:/app/docs_output
    networks:
      - jommcp-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8084/api/v1/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Web UI
  web-ui:
    build:
      context: ./apps/web-ui
      dockerfile: Dockerfile.dev
    container_name: jommcp-web-ui
    environment:
      - NEXT_PUBLIC_API_BASE_URL=http://localhost:${API_GATEWAY_PORT:-8000}
      - NEXT_PUBLIC_WS_BASE_URL=ws://localhost:${API_GATEWAY_PORT:-8000}
    ports:
      - "${WEB_UI_PORT:-3000}:3000"
    volumes:
      - ./apps/web-ui/src:/app/src
      - ./apps/web-ui/public:/app/public
    networks:
      - jommcp-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Monitoring with Prometheus (optional)
  prometheus:
    image: prom/prometheus:latest
    container_name: jommcp-prometheus
    ports:
      - "${PROMETHEUS_PORT:-9090}:9090"
    volumes:
      - ./infrastructure/monitoring/prometheus.yml:/etc/prometheus/prometheus.yml
      - prometheus_data:/prometheus
    networks:
      - jommcp-network
    restart: unless-stopped
    profiles:
      - monitoring

  # Grafana for dashboards (optional)
  grafana:
    image: grafana/grafana:latest
    container_name: jommcp-grafana
    ports:
      - "${GRAFANA_PORT:-3001}:3000"
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=${GRAFANA_PASSWORD:-admin}
    volumes:
      - grafana_data:/var/lib/grafana
      - ./infrastructure/monitoring/grafana/dashboards:/etc/grafana/provisioning/dashboards
      - ./infrastructure/monitoring/grafana/datasources:/etc/grafana/provisioning/datasources
    networks:
      - jommcp-network
    restart: unless-stopped
    profiles:
      - monitoring

volumes:
  postgres_data:
    driver: local
  redis_data:
    driver: local
  docs_data:
    driver: local
  prometheus_data:
    driver: local
  grafana_data:
    driver: local

networks:
  jommcp-network:
    driver: bridge
    name: jommcp-network
