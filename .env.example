# JomMCP Platform Configuration Template
# Copy this file to .env and customize the values

# =============================================================================
# ENVIRONMENT CONFIGURATION
# =============================================================================
ENVIRONMENT=development
DEBUG=true

# =============================================================================
# DATABASE CONFIGURATION
# =============================================================================
# PostgreSQL Database
DATABASE_URL=postgresql+asyncpg://mcphub:mcphub_dev_password@localhost:5432/mcphub
POSTGRES_DB=mcphub
POSTGRES_USER=mcphub
POSTGRES_PASSWORD=mcphub_dev_password
POSTGRES_PORT=5432

# =============================================================================
# CACHE CONFIGURATION
# =============================================================================
# Redis Cache
REDIS_URL=redis://localhost:6379/0
REDIS_PORT=6379

# =============================================================================
# SECURITY CONFIGURATION
# =============================================================================
# JWT Configuration (Generate with: openssl rand -hex 32)
JWT_SECRET_KEY=your-secret-key-here
JWT_ALGORITHM=HS256
JWT_ACCESS_TOKEN_EXPIRE_MINUTES=30
JWT_REFRESH_TOKEN_EXPIRE_DAYS=7

# =============================================================================
# API CONFIGURATION
# =============================================================================
API_V1_PREFIX=/api/v1
CORS_ORIGINS=["http://localhost:3000","http://localhost:8000"]

# =============================================================================
# SERVICE PORTS
# =============================================================================
API_GATEWAY_PORT=8000
REGISTRATION_SERVICE_PORT=8081
GENERATOR_SERVICE_PORT=8082
DEPLOYMENT_SERVICE_PORT=8083
DOCS_SERVICE_PORT=8084
WEB_UI_PORT=3000

# =============================================================================
# FRONTEND CONFIGURATION
# =============================================================================
NEXT_PUBLIC_API_BASE_URL=http://localhost:8000
NEXT_PUBLIC_WS_BASE_URL=ws://localhost:8000

# =============================================================================
# MONITORING CONFIGURATION (Optional)
# =============================================================================
PROMETHEUS_PORT=9090
GRAFANA_PORT=3001
GRAFANA_PASSWORD=admin

# =============================================================================
# OPTIONAL FEATURES
# =============================================================================
ENABLE_MONITORING=false
ENABLE_SSL=false

# =============================================================================
# PRODUCTION SETTINGS (Only for production environment)
# =============================================================================
# Uncomment and configure for production deployment

# SSL Configuration
# SSL_CERT_PATH=/path/to/cert.pem
# SSL_KEY_PATH=/path/to/key.pem

# External Database (Production)
# DATABASE_URL=postgresql+asyncpg://user:password@your-db-host:5432/dbname

# External Redis (Production)
# REDIS_URL=redis://your-redis-host:6379/0

# Email Configuration (for notifications)
# SMTP_HOST=smtp.gmail.com
# SMTP_PORT=587
# SMTP_USER=<EMAIL>
# SMTP_PASSWORD=your-app-password
# SMTP_TLS=true

# Logging Configuration
# LOG_LEVEL=INFO
# LOG_FORMAT=json

# Rate Limiting
# RATE_LIMIT_REQUESTS=100
# RATE_LIMIT_WINDOW=60

# File Upload Limits
# MAX_UPLOAD_SIZE=10485760  # 10MB in bytes
# ALLOWED_FILE_TYPES=["json","yaml","yml"]
